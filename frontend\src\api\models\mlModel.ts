import request from "@/utils/request";
import type { ModelConfig, RegModelResult } from "@/types/models";
import { AxiosHeaders } from "axios";
import { http } from "@/utils/http";
import type { ApiResponse } from "@/types/request";

// RabbitMQ异步响应类型
export interface MLModelAsyncResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message: string;
  result?: {
    modelType: string;
    accuracy?: number;
    metrics?: Record<string, any>;
    predictions?: any[];
    modelPath?: string;
    resultUrl?: string; // 用于打开结果页面的URL
  };
  createdAt: string;
  completedAt?: string;
}

// 同步响应类型（与线性模型一致）
export interface MLModelSyncResponse {
  code: number;
  msg: string;
  data: RegModelResult;
}

// 导出两种响应类型，在运行时根据环境变量决定使用哪种
export type MLModelResponse = MLModelAsyncResponse | MLModelSyncResponse;

// 构建树模型（根据环境变量决定使用同步或异步处理）
export const buildTreeModel = (data?: ModelConfig): Promise<any> => {
  const rabbitmqEnabled = import.meta.env.VITE_RABBITMQ_ENABLED === "true";

  if (rabbitmqEnabled) {
    // 使用RabbitMQ异步处理
    return http.post<ApiResponse<MLModelAsyncResponse>, ModelConfig>("/tree/build", {
      data: data
    });
  } else {
    // 使用同步处理
    return request.post<MLModelSyncResponse>("/tree/build", data, {
      headers: new AxiosHeaders({}),
      skipLoading: true,
    });
  }
};

// 构建其他机器学习模型（根据环境变量决定使用同步或异步处理）
export const buildMLModel = (data?: ModelConfig): Promise<any> => {
  const rabbitmqEnabled = import.meta.env.VITE_RABBITMQ_ENABLED === "true";

  if (rabbitmqEnabled) {
    // 使用RabbitMQ异步处理
    return http.post<ApiResponse<MLModelAsyncResponse>, ModelConfig>("/other/build", {
      data: data
    });
  } else {
    // 使用同步处理
    return request.post<MLModelSyncResponse>("/other/build", data, {
      headers: new AxiosHeaders({}),
      skipLoading: true,
    });
  }
};

// RabbitMQ相关函数（仅在启用RabbitMQ时可用）
export const getMLModelStatus = (taskId: string): Promise<ApiResponse<MLModelAsyncResponse>> => {
  return http.get<ApiResponse<MLModelAsyncResponse>, any>(`/mlModel/status/${taskId}`);
};

export const getMLModelHistory = (): Promise<ApiResponse<MLModelAsyncResponse[]>> => {
  return http.get<ApiResponse<MLModelAsyncResponse[]>, any>("/mlModel/history");
};

export const cancelMLModelTask = (taskId: string): Promise<ApiResponse<{ success: boolean }>> => {
  return http.post<ApiResponse<{ success: boolean }>, any>(`/mlModel/cancel/${taskId}`);
};
