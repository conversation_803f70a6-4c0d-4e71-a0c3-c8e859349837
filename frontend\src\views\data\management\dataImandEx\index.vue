<template>
  <div class="flex flex-col grow">
    <teleport :key="teleportKey" defer to="#laymenu">
      <div class="flex items-center gap-2">
        <el-select
          v-if="sheetNames.length > 0"
          v-model="selectedSheet"
          placeholder="选择工作表"
          style="width: 150px"
          @change="handleSheetChange"
        >
          <el-option
            v-for="name in sheetNames"
            :key="name"
            :label="name"
            :value="name"
          />
        </el-select>

        <el-button :disabled="!hasData" @click="handleExport">
          导出文件
        </el-button>
        <el-button :disabled="!hasData" @click="analyzeData">
          分析数据集
        </el-button>
        <el-button :disabled="!hasData" @click="toOpenVisualize">
          统计分析
        </el-button>

        <ModelSelector
          :disabled="!hasData"
          @buildLinearModel="handleModelChange"
          @buildMLModel="handleMLModelChange"
          @buildTreeModel="handleTreeModelChange"
        />

        <DataPreprocessing
          :disabled="!hasData"
          @processData="handlePreprocessing"
        />
      </div>
    </teleport>

    <el-card id="data-table-container" shadow="never" class="flex-1">
      <DataTable
        ref="dataTableRef"
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        height="100%"
        empty-text="请上传数据文件，并选择正确工作表"
        :settings="customSettings"
        @ready="handleTableReady"
        @change="handleDataChange"
      />
    </el-card>

    <!-- 对话框组件 -->
    <DataAnalyzeDialog :table-instance="tableInstance" />

    <LinearModelDialog
      :model-type="currentModelType"
      @confirm="handleModelConfirm"
    />

    <MLModelDialog
      :model-type="currentMLModelType"
      @confirm="handleMLModelConfirm"
    />

    <DataPreprocessDialog
      :process-type="currentPreprocessType"
      @confirm="handlePreprocessingConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  watch,
  watchEffect,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  nextTick,
} from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import * as XLSX from "xlsx";

// 组件导入
import { DataTable } from "@/components/dataProcessing/src/dataTable";
import type { TableInstance, TableColumn } from "@/components/dataProcessing/src/dataTable";
import ModelSelector from "./menu/modelSelector.vue";
import DataPreprocessing from "./menu/dataPreprocessing.vue";
import DataAnalyzeDialog from "@/components/dataProcessing/src/dataAnalyzeDialog/index.vue";
import LinearModelDialog from "@/components/modelManagement/src/linearModelDialog/index.vue";
import MLModelDialog from "@/components/modelManagement/src/mlModelDialog/index.vue";
import DataPreprocessDialog from "@/components/dataProcessing/src/dataPreprocessDialog/index.vue";

// API 导入
import { buildLinearModel } from "@/api/models/linearModel";
import { buildMLModel, buildTreeModel } from "@/api/models/mlModel";
import { reqDataFill, reqOutlierDetection } from "@/api/models/dataPreprocess";
import type { ModelConfig } from "@/types/models";
import type { LinearModelResponse } from "@/api/models/linearModel";
import type { MLModelResponse } from "@/api/models/mlModel";
import type { ApiResponse } from "@/types/request";
import type { PreprocessConfig } from "@/types/preprocess";

// Store 导入
import { useTableDataStore } from "@/store/modules/tableData";
import { useDialogStore } from "@/store/modules/dialog";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

// 性能监控导入
import { tablePerformance, memoryMonitor } from "@/utils/performance";
import { debounce } from "@pureadmin/utils";

// 初始化
const dialogStore = useDialogStore();
const tableDataStore = useTableDataStore();
const workspaceStore = useWorkspaceStoreHook();
const router = useRouter();

// Listen for Excel file selection from workspace
onMounted(() => {
  console.log('DataImandEx mounted');

  // Note: Excel file selection events are now handled globally in main.ts
  // We'll use a watcher to respond to workspace store changes instead

  // 监听导出当前文件事件
  window.addEventListener('export-current-file', handleExportCurrentFile);

  // 检查workspace store中是否有当前文件路径
  const currentFilePath = workspaceStore.getCurrentFilePath;
  if (currentFilePath && /\.(xlsx|xls|csv)$/i.test(currentFilePath)) {
    console.log('Found existing Excel file in workspace store:', currentFilePath);
    // 延迟处理，确保组件完全挂载
    setTimeout(() => {
      handleExcelFileFromWorkspace(currentFilePath);
    }, 200);
  }
});

onBeforeUnmount(() => {
  // Note: Global IPC listeners are cleaned up in main.ts
  window.removeEventListener('export-current-file', handleExportCurrentFile);
});

// 处理导出当前文件事件
const handleExportCurrentFile = () => {
  console.log('Export current file triggered');
  handleExport();
};

// Refs
const dataTableRef = ref<InstanceType<typeof DataTable>>();
const tableInstance = ref<TableInstance>();
const teleportKey = ref(0);

// 状态
const loading = ref(false);
const tableData = ref<any[][]>([]);
const tableColumns = ref<TableColumn[]>([]);
const sheetNames = ref<string[]>([]);
const selectedSheet = ref<string>("");
const sheetsData = ref<Record<string, { headers: string[]; content: any[][] }>>(
  {},
);
const currentModelType = ref<string>("");
const currentMLModelType = ref<string>("");
const currentPreprocessType = ref<string>("");

// 表格准备状态
const isTableReady = ref(false);

// 性能优化：数据缓存
const processedDataCache = new Map<string, { columns: TableColumn[]; data: any[][] }>();
const lastProcessedSheet = ref<string>("");
// 缓存workbook用于懒加载
const cachedWorkbook = ref<XLSX.WorkBook | null>(null);

// 自定义 Handsontable 设置
const customSettings = {
  columnSorting: true,
  rowHeights: 30,
  manualRowMove: true,
  manualColumnMove: true,
  readOnly: false,
  allowInsertRow: true,
  allowInsertColumn: true,
  contextMenu: true,
};

// 计算属性
const hasData = computed(() => tableData.value.length > 0);

// 判断当前模型是否为树模型
const isCurrentModelTreeModel = computed(() => {
  if (!currentMLModelType.value) return false;

  const treeModels = [
    "DecisionTreeRegressor",
    "RandomForestRegressor",
    "GradientBoostingRegressor",
    "XGBoost",
  ];

  return treeModels.includes(currentMLModelType.value);
});

// ResizeObserver
let resizeObserver: ResizeObserver | null = null;
const lastDimensions = ref<{ width: number; height: number } | null>(null);

// Handle Excel file from workspace - 定义在前面避免引用错误
const handleExcelFileFromWorkspace = async (filePath: string, retryCount = 0) => {
  console.log('Received Excel file from workspace:', filePath, 'retry:', retryCount);

  // 重试机制：如果表格还没准备好，等待一段时间后重试，最多重试10次
  if (!isTableReady.value) {
    if (retryCount < 10) {
      console.log('Table not ready, will retry in 200ms, attempt:', retryCount + 1);
      setTimeout(() => {
        handleExcelFileFromWorkspace(filePath, retryCount + 1);
      }, 200);
      return;
    } else {
      console.error('Max retry attempts reached, table still not ready');
      ElMessage.error('表格组件未准备就绪，请稍后重试');
      return;
    }
  }

  // 显示文件切换加载动画
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: `正在加载文件: ${filePath.split(/[/\\]/).pop()}`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // Read file content using IPC
    const fileBuffer = await window.ipcRenderer.invoke('fs:readFileBuffer', filePath);
    if (fileBuffer) {
      // Set current file path in workspace store
      workspaceStore.setCurrentFile(filePath);
      // Clear data modification status for new file
      workspaceStore.markDataAsSaved(filePath);

      await handleUploadDataFile(fileBuffer);
    }
  } catch (error) {
    console.error('Error loading Excel file from workspace:', error);
    ElMessage.error('加载Excel文件失败');
  } finally {
    // 关闭加载动画
    loadingInstance.close();
  }
};

// 处理文件上传 - 优化版本
const handleUploadDataFile = async (dataBinary: ArrayBuffer) => {
  loading.value = true;

  // 性能监控
  memoryMonitor.logMemoryUsage('Before File Upload');

  try {
    // 清空旧数据
    tableData.value = [];
    tableColumns.value = [];
    sheetNames.value = [];
    selectedSheet.value = "";
    sheetsData.value = {};
    cachedWorkbook.value = null; // 清空缓存的workbook

    // 强制更新表格
    if (dataTableRef.value) {
      dataTableRef.value.forceUpdate();
    }

    await nextTick();

    // 使用性能监控包装文件解析
    await tablePerformance.measureFileParse(async () => {
      // 优化的Excel读取配置
      const workbook = XLSX.read(dataBinary, {
        type: "array",
        // 性能优化选项
        cellDates: false, // 禁用日期解析
        cellNF: false, // 禁用数字格式解析
        cellStyles: false, // 禁用样式解析
        sheetStubs: false, // 禁用空单元格占位符
      });

      // 缓存workbook用于懒加载
      cachedWorkbook.value = workbook;
      sheetNames.value = workbook.SheetNames;

      if (sheetNames.value.length === 0) {
        ElMessage.error("文件没有工作表");
        return;
      }

      // 懒加载模式：只预处理第一个工作表，其他工作表按需加载
      const firstSheetName = sheetNames.value[0];
      const firstSheetData = parseSheetDataOptimized(workbook, firstSheetName);

      if (firstSheetData) {
        sheetsData.value[firstSheetName] = firstSheetData;
      }

      // 默认选择第一个工作表
      selectedSheet.value = firstSheetName;
      await handleSheetChange(firstSheetName);
    });
  } catch (error) {
    ElMessage.error("文件解析失败：" + error.message);
    console.error(error);
  } finally {
    loading.value = false;
    memoryMonitor.logMemoryUsage('After File Upload');
  }
};

// 优化的工作表数据解析函数
const parseSheetDataOptimized = (workbook: XLSX.WorkBook, sheetName: string) => {
  try {
    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) return null;

    // 限制数据大小以提升性能
    const maxRows = 10000;
    const maxCols = 100;

    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
      defval: null,
      range: `A1:${XLSX.utils.encode_col(maxCols - 1)}${maxRows}`,
    }) as any[][];

    // 清洗数据 - 移除完全空的行
    const cleanedData = jsonData.filter((row) =>
      row.some((cell) => cell !== null && cell !== undefined && cell !== ""),
    );

    if (cleanedData.length > 0) {
      // 找到实际有数据的最大列数
      let maxCols = 0;
      cleanedData.forEach(row => {
        for (let i = row.length - 1; i >= 0; i--) {
          if (row[i] !== null && row[i] !== undefined && row[i] !== "") {
            maxCols = Math.max(maxCols, i + 1);
            break;
          }
        }
      });

      // 如果没有找到有效数据，至少保留一列
      if (maxCols === 0) {
        maxCols = 1;
      }

      // 截取有效的列数据
      const trimmedData = cleanedData.map(row => row.slice(0, maxCols));

      // 生成列标题，只为有效列生成
      const headers = trimmedData[0].slice(0, maxCols).map(
        (h, i) => (h && h.toString().trim()) || `列${i + 1}`,
      );

      // 内容数据（去除标题行）
      const content = trimmedData.slice(1);

      return { headers, content };
    }

    return null;
  } catch (error) {
    console.error(`解析工作表 "${sheetName}" 失败:`, error);
    return null;
  }
};

// 优化工作表切换性能 - 支持懒加载
const handleSheetChange = async (sheetName: string) => {
  if (!sheetName) {
    console.warn("No sheet name provided");
    return;
  }

  loading.value = true;

  // 性能监控
  return await tablePerformance.measureSheetSwitch(async () => {
    try {
      // 检查是否有缓存的数据，如果没有则懒加载
      if (!sheetsData.value[sheetName]) {
        console.log("Lazy loading sheet data for:", sheetName);

        if (cachedWorkbook.value) {
          // 从缓存的workbook中懒加载工作表数据
          const sheetData = parseSheetDataOptimized(cachedWorkbook.value, sheetName);
          if (sheetData) {
            sheetsData.value[sheetName] = sheetData;
          } else {
            // 如果解析失败，创建空数据
            sheetsData.value[sheetName] = {
              headers: ["列1"],
              content: [],
            };
          }
        } else {
          console.warn("No cached workbook available for lazy loading");
          // 如果没有缓存的workbook，创建空数据
          sheetsData.value[sheetName] = {
            headers: ["列1"],
            content: [],
          };
        }
      }

      const sheetData = sheetsData.value[sheetName];

    // 检查是否为空表
    const isEmpty = !sheetData.content || sheetData.content.length === 0;

    if (isEmpty) {
      ElMessage.info(`工作表 "${sheetName}" 为空`);
    }

    // 检查缓存以提升性能
    const cacheKey = `${sheetName}_${sheetData.headers.join('_')}_${sheetData.content.length}`;
    let newColumns: TableColumn[];
    let newData: any[][];

    if (processedDataCache.has(cacheKey) && lastProcessedSheet.value === sheetName) {
      // 使用缓存数据
      const cached = processedDataCache.get(cacheKey)!;
      newColumns = cached.columns;
      newData = cached.data;
      console.log('Using cached data for sheet:', sheetName);
    } else {
      // 处理新数据 - 确保列数据的一致性
      newColumns = sheetData.headers.map((header, index) => ({
        data: index.toString(),
        title: header,
        type: "text" as const,
        width: 120, // 设置固定列宽以确保横向滚动条显示
      }));

      // 优化数据处理 - 确保每行数据的列数与标题一致
      if (isEmpty) {
        newData = [];
      } else {
        // 确保每行数据的长度与列标题数量一致
        const expectedCols = sheetData.headers.length;
        newData = sheetData.content.map(row => {
          const normalizedRow = [...row];
          // 如果行数据不足，用空字符串填充
          while (normalizedRow.length < expectedCols) {
            normalizedRow.push("");
          }
          // 如果行数据过多，截取到正确长度
          return normalizedRow.slice(0, expectedCols);
        });
      }

      // 缓存处理后的数据
      processedDataCache.set(cacheKey, { columns: newColumns, data: newData });
      lastProcessedSheet.value = sheetName;

      // 限制缓存大小，避免内存泄漏
      if (processedDataCache.size > 10) {
        const firstKey = processedDataCache.keys().next().value;
        processedDataCache.delete(firstKey);
      }
    }

    // 批量更新以减少重绘
    if (dataTableRef.value && tableInstance.value?.hotInstance) {
      try {
        // 使用批量操作减少重绘次数
        tableInstance.value.hotInstance.batch(() => {
          // 先更新设置
          tableInstance.value.hotInstance.updateSettings({
            colHeaders: sheetData.headers,
            columns: newColumns.map((col) => ({
              data: col.data,
              type: col.type,
            })),
          });
          // 再加载数据
          tableInstance.value.hotInstance.loadData(newData);
        });

        // 更新响应式数据（在DOM更新后）
        tableColumns.value = newColumns;
        tableData.value = newData;

        // 延迟刷新尺寸以避免布局抖动
        await nextTick();
        requestAnimationFrame(() => {
          if (tableInstance.value?.hotInstance && !tableInstance.value.hotInstance.isDestroyed) {
            tableInstance.value.hotInstance.refreshDimensions();
          }
        });
      } catch (error) {
        console.warn('Batch update failed, falling back to direct update:', error);
        // 回退到直接更新
        tableColumns.value = newColumns;
        tableData.value = newData;
        await nextTick();
      }
    } else {
      // 表格实例不存在时直接更新数据
      tableColumns.value = newColumns;
      tableData.value = newData;
      await nextTick();
    }

    // 更新 hasData 的判断逻辑
    if (!isEmpty) {
      console.log(`已切换到工作表: ${sheetName}`);
    }
  } catch (error) {
    ElMessage.error("切换工作表失败：" + error.message);
    console.error(error);
  } finally {
    loading.value = false;
  }
  }); // 闭合 measureSheetSwitch
};

// 处理表格准备就绪
const handleTableReady = async (instance: TableInstance) => {
  console.log('Table ready, instance:', instance);
  tableInstance.value = instance;
  isTableReady.value = true;

  // 设置尺寸
  if (dataTableRef.value) {
    updateTableDimensions();
  }
};

// 更新表格尺寸 - 优化性能
const updateTableDimensions = debounce(() => {
  const mainContent = document.querySelector(".main-content");
  if (!mainContent || !dataTableRef.value) return;

  const { height, width } = mainContent.getBoundingClientRect();
  const containerHeight = `${height - 50}px`;
  const containerWidth = `${width - 60}px`;

  // 检查尺寸是否真的发生了变化
  const currentDimensions = { width, height };
  if (lastDimensions.value &&
      Math.abs(currentDimensions.width - lastDimensions.value.width) < 3 &&
      Math.abs(currentDimensions.height - lastDimensions.value.height) < 3) {
    return; // 尺寸变化太小，跳过更新
  }

  lastDimensions.value = currentDimensions;
  dataTableRef.value.updateDimensions(containerWidth, containerHeight);
}, 16); // 瞬时响应，16ms防抖（约1帧时间）

// 处理数据变化
const handleDataChange = (changes: any) => {
  // 可以在这里处理数据变化
  console.log("Data changed:", changes);

  // 如果有当前文件路径，标记数据为已修改
  if (workspaceStore.getCurrentFilePath && changes && changes.length > 0) {
    workspaceStore.markDataAsModified(workspaceStore.getCurrentFilePath);
  }
};

// 导出文件
const handleExport = () => {
  if (!tableInstance.value) return;

  const filename = selectedSheet.value
    ? `${selectedSheet.value}_export_${Date.now()}.xlsx`
    : `export_${Date.now()}.xlsx`;

  tableInstance.value.exportData(filename);

  // 重置表格修改状态
  if (dataTableRef.value?.resetModifiedState) {
    dataTableRef.value.resetModifiedState();
  }

  // 标记数据为已保存（如果有当前文件路径）
  if (workspaceStore.getCurrentFilePath) {
    workspaceStore.markDataAsSaved(workspaceStore.getCurrentFilePath);
  }

  ElMessage.success("导出成功");
};

// 监视 teleport 的重新渲染
watchEffect(() => {
  teleportKey.value++;
});

// 监听当前文件路径变化，自动加载新文件
watch(() => workspaceStore.getCurrentFilePath, async (newPath, oldPath) => {
  console.log('DataImandEx: Current file path changed:', { newPath, oldPath });

  if (newPath && newPath !== oldPath && workspaceStore.isExcelFile(newPath)) {
    console.log('DataImandEx: Loading new Excel file:', newPath);

    // 检查当前表格是否有修改
    if (oldPath && dataTableRef.value?.hasModifications()) {
      try {
        await ElMessageBox.confirm(
          `文件 "${oldPath.split(/[/\\]/).pop()}" 已被修改，是否要保存更改？`,
          '确认切换文件',
          {
            confirmButtonText: '保存并切换',
            cancelButtonText: '不保存',
            distinguishCancelAndClose: true,
            type: 'warning'
          }
        );

        // 用户选择保存，触发导出操作
        handleExport();
        // 等待导出完成后再切换
        setTimeout(() => {
          handleExcelFileFromWorkspace(newPath);
        }, 500);
      } catch (action) {
        if (action === 'cancel') {
          // 用户选择不保存，直接切换
          handleExcelFileFromWorkspace(newPath);
        } else {
          // 用户点击了X或按了ESC，取消切换操作
          // 恢复到原来的文件
          workspaceStore.setCurrentFile(oldPath);
          return;
        }
      }
    } else {
      // 没有修改，直接切换
      handleExcelFileFromWorkspace(newPath);
    }
  }
}, { immediate: true }); // 改为immediate: true，这样组件挂载时就会检查当前文件

// 分析数据
const analyzeData = () => {
  if (!tableInstance.value) {
    ElMessage.warning("没有可分析的数据");
    return;
  }

  // 直接显示对话框，数据分析组件会实时从表格实例获取数据
  dialogStore.showDialog("dataAnalyze");
};

// 打开可视化页面
const toOpenVisualize = () => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));

  const { href } = router.resolve({ path: "/visualize" });
  window.open(href, "_blank");
};

// 处理模型变化
const handleModelChange = (modelType: string) => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  currentModelType.value = modelType;

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));
  dialogStore.showDialog("linearModel");
};

// 处理机器学习模型变化
const handleMLModelChange = (modelType: string) => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  currentMLModelType.value = modelType;

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));
  dialogStore.showDialog("mlModel");
};

// 处理树模型变化
const handleTreeModelChange = (modelType: string) => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  currentMLModelType.value = modelType;

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));
  dialogStore.showDialog("mlModel");
};

// 处理数据预处理
const handlePreprocessing = (processType: string) => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  currentPreprocessType.value = processType;

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));
  dialogStore.showDialog("preprocessData");
};

// 处理模型确认
const handleModelConfirm = async (modelConfig: ModelConfig) => {
  try {
    const res: ApiResponse<LinearModelResponse> =
      await buildLinearModel(modelConfig);

    if (res.code === 200) {
      const { href } = router.resolve({
        name: "LMModelResult",
        query: { result: JSON.stringify(res.data) },
      });
      window.open(href, "_blank");
    } else {
      ElMessage.error("模型构建失败");
    }
  } catch (err) {
    ElMessage.error("请求失败：" + (err as Error).message);
  }
};

// 处理树模型确认（根据环境变量决定使用同步或异步处理）
const handleTreeModelConfirm = async (modelConfig: ModelConfig) => {
  try {
    const rabbitmqEnabled = import.meta.env.VITE_RABBITMQ_ENABLED === "true";
    const res = await buildTreeModel(modelConfig);

    if (rabbitmqEnabled) {
      // RabbitMQ异步处理
      if (res.code === 200) {
        const { taskId, status, message } = res.data;
        ElMessage.success({
          message: `树模型构建任务已成功提交到队列，任务ID: ${taskId}。请等待系统通知。`,
          duration: 5000
        });
        console.log('Tree Model Task submitted:', { taskId, status, message });
      } else {
        ElMessage.error("树模型构建任务提交失败");
      }
    } else {
      // 同步处理
      if (res.code === 200) {
        const { href } = router.resolve({
          name: "MLModelResult",
          query: { result: JSON.stringify(res.data) },
        });
        window.open(href, "_blank");
      } else {
        ElMessage.error("树模型构建失败");
      }
    }
  } catch (err) {
    ElMessage.error("请求失败：" + (err as Error).message);
  }
};

// 处理机器学习模型确认（统一处理，根据模型类型决定使用哪个API）
const handleMLModelConfirm = async (modelConfig: ModelConfig) => {
  console.log('handleMLModelConfirm called with:', modelConfig);
  console.log('Current model type:', currentMLModelType.value);

  // 判断是否为树模型
  const treeModels = [
    "DecisionTreeRegressor",
    "RandomForestRegressor",
    "GradientBoostingRegressor",
    "XGBoost",
  ];

  const isTreeModel = treeModels.includes(currentMLModelType.value);
  console.log('Is tree model:', isTreeModel);

  try {
    let res;
    let modelTypeName;

    if (isTreeModel) {
      console.log('Calling buildTreeModel...');
      res = await buildTreeModel(modelConfig);
      modelTypeName = "树模型";
    } else {
      console.log('Calling buildMLModel...');
      res = await buildMLModel(modelConfig);
      modelTypeName = "其他模型";
    }

    console.log('API response:', res);

    // 同步处理（不考虑RabbitMQ）
    if (res.code === 200) {
      const { href } = router.resolve({
        name: "MLModelResult",
        query: { result: JSON.stringify(res.data) },
      });
      window.open(href, "_blank");
      ElMessage.success(`${modelTypeName}构建成功`);
    } else {
      ElMessage.error(`${modelTypeName}构建失败`);
    }
  } catch (err) {
    console.error('Model build error:', err);
    ElMessage.error("请求失败：" + (err as Error).message);
  }
};

// 处理预处理确认
const handlePreprocessingConfirm = async (config: PreprocessConfig) => {
  if (!tableInstance.value) return;

  const originalData = tableInstance.value.getData();
  const originalRowCount = originalData.length;

  try {
    if (currentPreprocessType.value === "missingValue") {
      // 缺失值处理
      const res = await reqDataFill(config);

      if (res.code === 200) {
        const { data, meta } = res.data;

        // 更新列配置
        tableColumns.value = meta.headers.all.map((header, index) => ({
          data: index.toString(),
          title: header,
          type: "text",
          width: 120, // 保持固定列宽
        }));

        // 优化数据更新 - 使用批量操作
        if (tableInstance.value?.hotInstance) {
          try {
            tableInstance.value.hotInstance.batch(() => {
              tableInstance.value.hotInstance.updateSettings({
                colHeaders: meta.headers.all,
                columns: tableColumns.value.map((col) => ({
                  data: col.data,
                  type: col.type,
                })),
              });
              tableInstance.value.hotInstance.loadData(data);
            });
            // 更新响应式数据
            tableData.value = data;
          } catch (error) {
            console.warn('Batch update failed, using fallback:', error);
            // 回退方案
            tableData.value = [...data];
            await nextTick();
            if (dataTableRef.value) {
              dataTableRef.value.forceUpdate();
            }
          }
        } else {
          // 表格实例不存在时的回退方案
          tableData.value = [...data];
          await nextTick();
          if (dataTableRef.value) {
            dataTableRef.value.forceUpdate();
          }
        }

        // 提示信息
        if (config.preprocess.algorithm.name === "DropNA") {
          const deletedRows = originalRowCount - data.length;
          ElMessage.success(`已删除 ${deletedRows} 行缺失数据`);
        } else {
          ElMessage.success("数据预处理成功");
        }
      } else {
        ElMessage.error("数据预处理失败");
      }
    } else {
      // 异常值检测
      const res = await reqOutlierDetection(config);

      if (res.code !== 200) {
        await ElMessageBox.alert("异常值检测失败", "错误", {
          confirmButtonText: "确定",
          type: "error",
        });
        return;
      }

      const labels = res.data.labels;
      const outlierRows = labels
        .map((label: number, index: number) => (label === -1 ? index : -1))
        .filter((index: number) => index !== -1);

      if (outlierRows.length === 0) {
        await ElMessageBox.alert("异常值检测完成，未发现异常值", "检测完成", {
          confirmButtonText: "确定",
          type: "success",
        });
        return;
      }

      const rowText = outlierRows.map((i: number) => `${i + 1}`).join(",");
      const msg = `检测到以下异常值行号：${rowText}，是否删除这些行？`;

      await ElMessageBox.confirm(msg, "异常值检测结果", {
        confirmButtonText: "确定",
        type: "warning",
      });

      // 删除异常值行 - 优化性能
      const filteredData = originalData.filter(
        (_, index) => !outlierRows.includes(index),
      );

      // 优化数据更新 - 使用批量操作
      if (tableInstance.value?.hotInstance) {
        try {
          tableInstance.value.hotInstance.batch(() => {
            tableInstance.value.hotInstance.loadData(filteredData);
          });
          // 更新响应式数据
          tableData.value = filteredData;
        } catch (error) {
          console.warn('Batch update failed for outlier removal:', error);
          // 回退方案
          tableData.value = [...filteredData];
          await nextTick();
          if (dataTableRef.value) {
            dataTableRef.value.forceUpdate();
          }
        }
      } else {
        // 表格实例不存在时的回退方案
        tableData.value = [...filteredData];
        await nextTick();
        if (dataTableRef.value) {
          dataTableRef.value.forceUpdate();
        }
      }

      ElMessage.success("异常值已删除");
    }
  } catch (err) {
    if (err !== "cancel") {
      ElMessage.error("请求失败：" + err.message);
    }
  }
};

// 生命周期
onMounted(() => {
  // 设置 ResizeObserver
  resizeObserver = new ResizeObserver(() => {
    updateTableDimensions();
  });

  const mainContent = document.querySelector(".main-content");
  if (mainContent) {
    resizeObserver.observe(mainContent);
  }
});

onUnmounted(() => {
  // 清理资源
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // 清空数据
  tableData.value = [];
  tableColumns.value = [];
  sheetNames.value = [];
  selectedSheet.value = "";
  sheetsData.value = {};

  // 清理缓存以释放内存
  processedDataCache.clear();
  lastProcessedSheet.value = "";
});
</script>

<style lang="scss" scoped>
// 主容器样式
.flex-1 {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 卡片容器
#data-table-container {
  flex: 1;
  overflow: hidden;

  :deep(.el-card__body) {
    height: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }
}

// 按钮间距
:deep(.el-button + .el-button) {
  margin-left: 0px;
}

// 表格容器样式
:deep(.data-table-container) {
  height: 100%;
}

:deep(.data-table-body) {
  flex: 1;
  height: 100%;
  overflow: visible; // 允许显示滚动条
}

:deep(.table-content-wrapper) {
  height: 100%;
}

// 空状态样式
:deep(.empty-state) {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-empty__image) {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Handsontable 滚动条样式优化
:deep(.handsontable) {
  // 确保表格容器可以正常滚动
  .ht_master {
    // 主容器允许溢出
    overflow: visible !important;

    .wtHolder {
      // 主表格区域显示滚动条
      overflow: auto !important;
      // 确保滚动条可见且美观
      scrollbar-width: thin;

      // 亮色模式滚动条
      scrollbar-color: #c1c1c1 #f1f1f1;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 4px;

        &:hover {
          background: #adb5bd;
        }

        &:active {
          background: #6c757d;
        }
      }

      // 滚动条角落
      &::-webkit-scrollbar-corner {
        background: #f8f9fa;
      }
    }

    // 确保水平滚动条区域可见
    .wtHider {
      overflow: visible !important;
    }
  }
}

// 深色模式下的滚动条样式覆盖
html.dark :deep(.handsontable) {
  .ht_master .wtHolder {
    // 深色模式滚动条
    scrollbar-color: rgb(63 64 66) transparent;

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
    }

    &::-webkit-scrollbar-thumb {
      background: rgb(63 64 66);

      &:hover {
        background: rgb(92 93 96);
      }

      &:active {
        background: rgb(120 121 124);
      }
    }

    &::-webkit-scrollbar-corner {
      background: var(--el-fill-color-lighter);
    }
  }

  // 表头区域不显示滚动条，但保持同步滚动
  .ht_clone_top {
    .wtHolder {
      overflow-x: hidden !important; // 隐藏水平滚动条
      overflow-y: hidden !important; // 隐藏垂直滚动条
    }
  }

  // 左侧固定列区域
  .ht_clone_left {
    .wtHolder {
      overflow-x: hidden !important; // 隐藏水平滚动条
      overflow-y: hidden !important; // 隐藏垂直滚动条
    }
  }

  // 左上角区域
  .ht_clone_corner {
    .wtHolder {
      overflow: hidden !important; // 完全隐藏滚动条
    }
  }
}
</style>
